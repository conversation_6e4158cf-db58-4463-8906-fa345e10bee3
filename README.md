# ABUS Hubspot
This folder contains all custom files for the Hubspot CMS.

## Initial Setup
### 1. Install the HubSpot CLI
`npm install -g @hubspot/cli@latest`

### 2. Configure the local development tools
Run `hs account auth` to connect the tools to your HubSpot account.

### 3. Setup structure
It is recommended to create a parent folder like this:

```bash
mkdir hubspot # or any other name
cd hubspot
```

### 4. Clone the repo
```bash
git clone ssh://******************************:2289/abus/hubspot-cms.git ABUS
```

### 5. Download other files (optional)
It could be helpful to download other files from Hubspot to get a complete overview and use them for reference.
Run `hs fetch . .` to download all files.

## Local Development
It's possible to work in the design manager, but the recommended way is to work locally.
This repo is always the source of truth.

### Download the project
Run `hs fetch ABUS . -o` to download all files.

### Watch for changes
When developing you can watch for changes by running `hs watch ABUS ABUS --initial-upload`.
