.hero-slider-wrapper {
    width: 100%;
    position: relative;
}

.hero-slider {
    width: 100%;
}

.hero-slide-image,
.hero-slide-video {
    width: 100%;
    overflow: hidden;
}

.hero-slide-image img {
    margin: 0 auto;
    max-height: calc(100vh - var(--header-height)) !important;
    width: 100%;
    object-fit: contain;
}

.hero-slide-video {
    position: relative;
    width: 100%;
    height: auto;
}

.hero-slide-video iframe,
.hero-slide-video video {
    width: 100%;
    height: auto;
    max-height: calc(100vh - var(--header-height));
}

.hero-slide-video .splide__arrow {
    background-color: white;
}
