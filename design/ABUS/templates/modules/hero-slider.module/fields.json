[{"id": "df607a72-181d-64e3-c861-4e55f4406a1f", "name": "slide", "label": "Slide", "required": false, "locked": false, "occurrence": {"min": 1, "max": null, "sorting_label_field": null, "default": 1}, "children": [{"id": "3275efed-2e0d-3699-30a2-aa2a10ea479b", "name": "type", "label": "Type", "required": true, "locked": false, "display": "radio", "choices": [["image", "Image"], ["video", "Video"]], "multiple": false, "reordering_enabled": true, "preset": null, "type": "choice", "display_width": null, "default": "image"}, {"id": "8e41917d-3b77-4a53-b245-cb7271762a0e", "name": "hero_image", "label": "Bild", "required": false, "locked": false, "visibility": {"controlling_field": null, "controlling_field_path": "slide.type", "controlling_value_regex": "image", "property": null, "operator": "EQUAL", "access": null}, "responsive": true, "resizable": false, "show_loading": false, "type": "image", "display_width": null, "default": {"size_type": "auto", "src": "", "alt": null, "loading": "eager"}}, {"id": "62db3b12-ab61-8757-6ef4-b32bc29856d0", "name": "hero_image_mobile", "label": "Bild (mobil)", "required": false, "locked": false, "visibility": {"controlling_field": null, "controlling_field_path": "slide.type", "controlling_value_regex": "image", "property": null, "operator": "EQUAL", "access": null}, "responsive": true, "resizable": false, "show_loading": false, "type": "image", "display_width": null, "default": {"size_type": "auto", "src": "", "alt": null, "loading": "eager"}}, {"id": "0855a47b-c155-3311-8e5d-cc2d16656f76", "name": "background_color", "label": "Farbe", "required": false, "locked": false, "visibility": {"controlling_field": null, "controlling_field_path": "slide.type", "controlling_value_regex": "image", "property": null, "operator": "EQUAL", "access": null}, "type": "color", "display_width": null, "default": {"color": null, "opacity": null}}, {"picker": "video", "id": "3fcea5d8-3c26-4532-57a3-4d2df6181378", "name": "video", "label": "Video", "required": false, "locked": false, "visibility": {"controlling_field": null, "controlling_field_path": "slide.type", "controlling_value_regex": "video", "property": null, "operator": "EQUAL", "access": null}, "type": "file", "display_width": null}], "tab": "CONTENT", "expanded": false, "group_occurrence_meta": null, "type": "group", "display_width": null, "default": [{"type": "image", "hero_image": {"size_type": "auto", "src": "", "alt": null, "loading": "eager"}, "hero_image_mobile": {"size_type": "auto", "src": "", "alt": null, "loading": "eager"}, "background_color": {"color": null, "opacity": null}}]}]