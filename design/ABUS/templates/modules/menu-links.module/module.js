const header = document.querySelector('.header');
const menuToggleButton = document.querySelector('.menu-links-toggle');
const menuItems = header.querySelectorAll('.menu-links-item');
const mediaQueryMd = window.matchMedia('(min-width: 768px)');

function closeMenu() {
  menuToggleButton.classList.remove('active');
  header.classList.remove('menu-links-mobile-active');
}

function toggleMenu() {
  menuToggleButton.classList.toggle('active');
  header.classList.toggle('menu-links-mobile-active');
}

if (menuToggleButton && header) {
  menuToggleButton.addEventListener('click', toggleMenu);

  menuItems.forEach(item => {
    item.addEventListener('click', closeMenu);
  });

  // Use media query listener for more accurate responsiveness
  mediaQueryMd.addEventListener('change', e => {
    if (e.matches) {
      closeMenu();
    }
  });
}
