.menu-links {
    display: none;
    flex-direction: column;
    padding: 0 15px;
    margin-top: 25px;
    gap: 25px;
}

@media screen and (min-width: 768px) {
    .menu-links {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0;
        margin: 0;
        gap: 50px;
    }
}

.menu-links-item-link {
    color: var(--color-primary);
    font-weight: 600;
    text-decoration: none;
    font-size: 1rem;
}

@media screen and (min-width: 768px) {
    .menu-links-item-link {
        color: var(--color-white);
    }
}

.menu-links-item-link:hover {
    text-decoration: none;
}

.menu-links-item-link svg {
    fill: var(--color-primary);
    height: 30px;
    width: auto;
}

@media screen and (min-width: 768px) {
    .menu-links-item-link svg {
        fill: var(--color-white);
    }
}

.menu-links-toggle {
    color: var(--color-white);
    outline: none;
    border: none;
    background: none;
}

@media screen and (min-width: 768px) {
    .menu-links-toggle {
        display: none;
    }
}

.menu-links-toggle .menu-links-toggle--close {
    display: none;
}

.menu-links-toggle.active .menu-links-toggle--close {
    display: inline-block;
}

.menu-links-toggle.active .menu-links-toggle--open {
    display: none;
}

.header.menu-links-mobile-active .menu-links-wrapper {
    position: fixed;
    top: var(--header-height);
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--color-white);
    z-index: 1000;
}

.header.menu-links-mobile-active .menu-links {
    display: flex;
}
