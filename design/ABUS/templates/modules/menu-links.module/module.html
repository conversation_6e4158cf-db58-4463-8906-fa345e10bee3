<div class="menu-links-wrapper">
    <ul class="menu-links list-reset">
        {% for item in module.menu_group %}
            <li class="menu-links-item">
                <a
                    {% set href = item.menu_link.url.href %}
                    {% if item.menu_link.url.type is equalto "CALL_TO_ACTION"  %}
                        href="{{ href }}" {# The href here is not escaped as it is not editable and functions as a JavaScript call to the associated CTA #}
                    {% else %}
                        href="{{ href|escape_url }}"
                    {% endif %}
                    {% if item.menu_link.open_in_new_tab %}
                        target="_blank"
                    {% endif %}
                    {% if item.menu_link.rel %}
                        rel="{{ item.menu_link.rel|escape_attr }}"
                    {% endif %}
                    class="menu-links-item-link flex flex-row gap-15 items-center"
                >
                    {% icon
                        name="{{ item.menu_icon.name }}"
                        style="{{ item.menu_icon.type }}"
                        unicode="{{ item.menu_icon.unicode }}"
                        icon_set="{{ item.menu_icon.icon_set }}"
                    %}
                    {% if item.menu_link.url.type is equalto "EMAIL_ADDRESS" %}
                        {% set href = "mailto:" + href %}
                    {% elif item.menu_link.url.type is equalto "PHONE_NUMBER" %}
                        {% set href = "tel:" + href %}
                    {% endif %}
                    {{ item.menu_text }}
                </a>
            </li>
        {% endfor %}
    </ul>
</div>

<button class="menu-links-toggle" aria-label="Toggle menu">
    <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        viewBox="0 0 24 24"
        width="28"
        height="28"
        class="menu-links-toggle--open"
    >
        <path d="M4 12h16M4 18h16M4 6h16"/>
    </svg>
    <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        viewBox="0 0 24 24"
        width="28"
        height="28"
        class="menu-links-toggle--close"
    >
        <path d="M18 6 6 18M6 6l12 12"/>
    </svg>
</button>
