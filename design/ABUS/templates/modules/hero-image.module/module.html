{% if module.hero_image.src %}
    <div class="hero-image-wrapper"{% if module.background_color.color %} style="background-color: rgba({{ module.background_color.color | convert_rgb }}, {{ module.background_color.opacity }});"{% endif %}>
        {% set loadingAttr = module.hero_image.loading != 'disabled' ? 'loading="{{ module.hero_image.loading|escape_attr }}"' : '' %}
        <div class="hero-image">
            <picture>
                {% if module.hero_image_mobile.src %}
                    <source srcset="{{ module.hero_image_mobile.src|escape_url }}" media="(max-width: 767px)">
                {% endif %}
                <source srcset="{{ module.hero_image.src|escape_url }}">
                <img src="{{ module.hero_image.src|escape_url }}" alt="{{ module.hero_image.alt|escape_attr }}" {{ loadingAttr }}>
            </picture>
        </div>
    </div>
{% endif %}
