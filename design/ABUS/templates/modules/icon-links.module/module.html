<div class="icon-links">
    {% for item in module.link %}
        {% set href = item.link.url.href %}
        {% if item.link.url.type is equalto "EMAIL_ADDRESS" %}
            {% set href = "mailto:" + href %}
        {% elif item.link.url.type is equalto "PHONE_NUMBER" %}
            {% set href = "tel:" + href %}
        {% endif %}

        {% if item.link && item.icon %}
            <a  {% if item.link.url.type is equalto "CALL_TO_ACTION"  %}
                    href="{{ href }}" {# The href here is not escaped as it is not editable and functions as a JavaScript call to the associated CTA #}
                {% else %}
                    href="{{ href|escape_url }}"
                {% endif %}
                {% if item.link.open_in_new_tab %}
                    target="_blank"
                {% endif %}
                {% if item.link.rel %}
                    rel="{{ item.link.rel|escape_attr }}"
                {% endif %}
            >
                {% set loadingAttr = item.icon.loading != 'disabled' ? 'loading="{{ item.icon.loading|escape_attr }}"' : '' %}
                <img src="{{ item.icon.src|escape_url }}" alt="{{ item.icon.alt|escape_attr }}" height="{{ module.style.icon_size }}" {{ loadingAttr }}>
            </a>
        {% endif %}
    {% endfor %}
</div>
