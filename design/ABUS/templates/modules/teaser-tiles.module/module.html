<div class="teaser-tiles">
    {% for item in module.teaser_tile %}
        <div
            id="teaser-tile-{{ module.uid }}-{{ loop.index0 }}"
            class="teaser-tiles__item"
            {% if item.inner_content %}
                data-collapse-target="#teaser-tile-inner-{{ module.uid }}-{{ loop.index0 }}"
            {% endif %}
        >
            <span class="teaser-tiles__item-text{% if item.inner_content %} teaser-tiles__item-text--has-hover{% endif %}">
                {% inline_rich_text field="content" value="{{ item.content }}" %}
            </span>

            {% set href = item.link.url.href %}
            {% if item.link.url.type is equalto "EMAIL_ADDRESS" %}
                {% set href = "mailto:" + href %}
            {% elif item.link.url.type is equalto "PHONE_NUMBER" %}
                {% set href = "tel:" + href %}
            {% endif %}
            {% if href and item.link_text %}
                <a
                    {% if item.link.url.type is equalto "CALL_TO_ACTION"  %}
                        href="{{ href }}" {# The href here is not escaped as it is not editable and functions as a JavaScript call to the associated CTA #}
                    {% else %}
                        href="{{ href|escape_url }}"
                    {% endif %}
                    {% if item.link.open_in_new_tab %}
                        target="_blank"
                    {% endif %}
                    {% if item.link.rel %}
                        rel="{{ item.link.rel|escape_attr }}"
                    {% endif %}
                    class="btn btn-primary"
                >
                    {% inline_text field="link_text" value="{{ item.link_text }}" %}
                </a>
            {% endif %}
        </div>
        {% if item.inner_content %}
            <div id="teaser-tile-inner-{{ module.uid }}-{{ loop.index0 }}" class="teaser-tiles__item--inner-content">
                <div class="overflow-hidden">
                    {% inline_rich_text field="inner_content" value="{{ item.inner_content }}" %}
                </div>
            </div>
        {% endif %}
    {% endfor %}
</div>
