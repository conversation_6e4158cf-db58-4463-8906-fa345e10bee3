document.addEventListener('DOMContentLoaded', function () {
    function reorderCollapseElements() {
        const grids = document.querySelectorAll('.teaser-tiles');

        grids.forEach((grid) => {
            const collapseElements = grid.querySelectorAll('.teaser-tiles__inner-content');

            // Store original positions
            const originalPositions = new Map();
            collapseElements.forEach((collapse) => {
                originalPositions.set(collapse, collapse.previousElementSibling);
            });

            // Get current columns based on viewport
            const getCurrentColumns = () => {
                if (window.innerWidth >= 992) return 3;
                if (window.innerWidth >= 576) return 2;
                return 1;
            };

            const reposition = () => {
                const currentCols = getCurrentColumns();
                const teaserTiles = Array.from(grid.children).filter((el) => el.classList.contains('teaser-tiles__item'));

                // Always reset to original positions
                collapseElements.forEach((collapse) => {
                    const originalBox = originalPositions.get(collapse);
                    if (originalBox && originalBox.nextElementSibling !== collapse) {
                        originalBox.insertAdjacentElement('afterend', collapse);
                    }
                });

                // Only skip custom reordering for single column
                if (currentCols === 1) return;

                // Calculate new positions
                teaserTiles.forEach((tile, index) => {
                    const collapseId = `teaser-tile-inner-${tile.id.split('-').splice(2).join('-')}`;
                    const collapse = grid.querySelector(`#${collapseId}`);

                    if (collapse) {
                        const row = Math.floor(index / currentCols);
                        const lastBoxInRow = Math.min((row + 1) * currentCols - 1, teaserTiles.length - 1);
                        const targetBox = teaserTiles[lastBoxInRow];

                        if (targetBox && targetBox.nextElementSibling !== collapse) {
                            targetBox.insertAdjacentElement('afterend', collapse);
                        }
                    }
                });
            };

            reposition();

            // Reposition on window resize
            let resizeTimeout;
            window.addEventListener('resize', () => {
                clearTimeout(resizeTimeout);
                resizeTimeout = window.setTimeout(reposition, 150);
            });
        });
    }

    const items = document.querySelectorAll('.teaser-tiles__item');
    items.forEach((item) => {
        item.addEventListener('click', function () {
            const id = item.getAttribute('id');
            const innerId = `teaser-tile-inner-${id.split('-').slice(2).join('-')}`;
            const content = document.getElementById(innerId);
            const allContents = document.querySelectorAll('.teaser-tiles__item--inner-content');

            if (!content) return;

            const isOpen = content.classList.contains('open');

            // Close all
            allContents.forEach(el => {
                if (el.classList.contains('open')) {
                    el.classList.add('animating');
                    el.classList.remove('open');

                    el.addEventListener('transitionend', function handler() {
                        el.classList.remove('animating');
                        el.removeEventListener('transitionend', handler);
                    }, { once: true });
                } else {
                    el.classList.remove('animating');
                    el.classList.remove('open');
                }
            });

            // Re-open only if it wasn't already open
            if (!isOpen) {
                content.classList.add('animating');

                // Force reflow to ensure transition is recognized
                void content.offsetWidth;

                content.classList.add('open');

                content.addEventListener('transitionend', function handler() {
                    content.classList.remove('animating');
                    content.removeEventListener('transitionend', handler);
                }, { once: true });
            }
        });
    });

    reorderCollapseElements();
});
