.teaser-tiles {
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    gap: 15px;
    margin: 25px 0 50px 0;
}

@media (min-width: 576px) {
    .teaser-tiles {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

@media (min-width: 992px) {
    .teaser-tiles {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}

.teaser-tiles__item {
    aspect-ratio: 1 / 1;
    width: 100%;
    color: var(--color-primary);
    border: 3px solid #dee2e6;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    gap: 15px;
    font-size: 2rem;
    padding: 50px;
    font-weight: bold;
    line-height: 1.5;
    transition: all 0.15s ease-in-out;
}

.teaser-tiles__item[data-collapse-target]:hover {
    transform: scale(1.025);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    cursor: pointer;
}

@media (min-width: 576px) {
    .teaser-tiles__item {
        padding: 25px;
        font-size: 1.5rem;
    }
}

@media (min-width: 768px) {
    .teaser-tiles__item {
        font-size: 2rem;
        padding: 50px;
    }
}

@media (min-width: 992px) {
    .teaser-tiles__item {
        font-size: 1.75rem;
    }
}

@media (min-width: 1200px) {
    .teaser-tiles__item {
        font-size: 2rem;
    }
}

@media (min-width: 1400px) {
    .teaser-tiles__item {
        font-size: 2.25rem;
        padding: 75px;
    }
}

.teaser-tiles__item--inner-content {
    display: none;
    overflow: hidden;
    grid-column: 1 / -1;
}

.teaser-tiles__item--inner-content.animating {
    display: grid;
    grid-template-rows: 0fr;
    transition: grid-template-rows 0.2s ease-out;
}

.teaser-tiles__item--inner-content.animating.open {
    grid-template-rows: 1fr;

    @starting-style {
        grid-template-rows: 0fr;
    }
}

.teaser-tiles__item--inner-content.open {
    display: grid;
    grid-template-rows: 1fr;
}

.teaser-tiles__item:nth-of-type(1n) {
    background-color: var(--color-secondary-accent);
}

.teaser-tiles__item:nth-of-type(2n) {
    color: #fff;
    background-color: var(--color-primary);
}

.teaser-tiles__item:nth-of-type(2n) .teaser-tiles__item-text--hover {
    color: white;
}

.teaser-tiles__item:nth-of-type(3n) {
    background-color: #fff;
}

@media (min-width: 576px) and (max-width: 991.98px) {
    .teaser-tiles__item:nth-of-type(3n) {
        color: #fff;
        background-color: var(--color-primary);
    }

    .teaser-tiles__item:nth-of-type(3n) .teaser-tiles__item-text--hover {
        color: white;
    }
}

.teaser-tiles__item:nth-of-type(4n) {
    color: #fff;
    background-color: var(--color-primary);
}

.teaser-tiles__item:nth-of-type(4n) .teaser-tiles__item-text--hover {
    color: white;
}

@media (min-width: 576px) and (max-width: 991.98px) {
    .teaser-tiles__item:nth-of-type(4n) {
        color: var(--color-primary);
        background-color: #fff;
    }

    .teaser-tiles__item:nth-of-type(4n) .teaser-tiles__item-text--hover {
        color: black;
    }
}

.teaser-tiles__item:nth-of-type(5n) {
    background-color: #fff;
}

.teaser-tiles__item:nth-of-type(6n) {
    background-color: var(--color-secondary-accent);
}

.teaser-tiles__item p {
    margin: 0;
}

.teaser-tiles__item b,
.teaser-tiles__item strong {
    background-color: var(--color-secondary);
    border-radius: 10px;
    padding: 4px 10px;
    margin: 0 -10px;
}
