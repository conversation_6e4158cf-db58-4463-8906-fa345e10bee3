.hero-banner {
    position: relative;
    min-height: 300px;
    display: flex;
    align-items: center;
}

@media screen and (min-width: 768px) {
    .hero-banner {
        min-height: 400px;
    }
}

@media screen and (min-width: 1024px) {
    .hero-banner {
        min-height: 600px;
    }
}

.hero-banner__image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
}

.hero-banner__content {
    padding: 90px 60px;
    background-color: rgba(2, 90, 170, 0.6);
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

@media screen and (min-width: 576px) {
    .hero-banner__content {
        position: relative;
        margin-top: 50px;
        margin-bottom: 50px;
        max-width: 600px;
        width: auto;
        height: auto;
    }
}

.hero-banner__content h2 {
    font-size: 2rem;
    color: var(--color-white);
}

@media screen and (min-width: 1024px) {
    .hero-banner__content h2 {
        font-size: 2.5rem;
    }
}
