<div class="hero-banner full-width">
    {% if module.background_image.src %}
        {% set loadingAttr = module.background_image.loading != 'disabled' ? 'loading="{{ module.background_image.loading|escape_attr }}"' : '' %}
        <img src="{{ module.background_image.src|escape_url }}" alt="{{ module.background_image.alt|escape_attr }}" class="hero-banner__image" {{ loadingAttr }}>
    {% endif %}
    <div class="hero-banner__content container">
        {% if module.heading %}
            <h2>{{ module.heading }}</h2>
        {% endif %}

        {% set href = module.link.url.href %}
        {% if module.link.url.type is equalto "EMAIL_ADDRESS" %}
            {% set href = "mailto:" + href %}
        {% elif module.link.url.type is equalto "PHONE_NUMBER" %}
            {% set href = "tel:" + href %}
        {% endif %}
        {% if module.link && module.link_text %}
            <a  {% if module.link.url.type is equalto "CALL_TO_ACTION"  %}
                    href="{{ href }}" {# The href here is not escaped as it is not editable and functions as a JavaScript call to the associated CTA #}
                {% else %}
                    href="{{ href|escape_url }}"
                {% endif %}
                {% if module.link.open_in_new_tab %}
                    target="_blank"
                {% endif %}
                {% if module.link.rel %}
                    rel="{{ module.link.rel|escape_attr }}"
                {% endif %}
                class="btn btn-white"
            >
                {{ module.link_text }}
            </a>
        {% endif %}
    </div>
</div>
